<?php
/**
 * Tabbed interface for shipping methods and free shipping thresholds
 * @var \Coditron\CustomShippingRate\Block\TabbedRates $block
 */
$isPartner = $block->isSeller();
?>

<div class="wk-mpsellercategory-container">
    <?php if ($isPartner == 1): ?>
        <!-- Tab Navigation -->
        <div class="wk-mp-tabs-container">
            <div class="wk-mp-tabs-nav">
                <button class="wk-mp-tab-btn active" data-tab="shipping-methods">
                    <?= $escaper->escapeHtml(__('Manage Shipping Methods')) ?>
                </button>
                <button class="wk-mp-tab-btn" data-tab="free-shipping-threshold">
                    <?= $escaper->escapeHtml(__('Free Shipping Threshold')) ?>
                </button>
            </div>
        </div>

        <!-- Tab Content -->
        <div class="wk-mp-tab-content">
            <!-- Shipping Methods Tab -->
            <div id="shipping-methods" class="wk-mp-tab-pane active">
                <div class="page-main-actions">
                    <div class="page-actions-placeholder"></div>
                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                        <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Shipping Method")); ?>'>
                            <div class="page-actions-buttons">
                                <button id="add-shipping-method" title='<?= $escaper->escapeHtml(__("Add New Method")); ?>' type="button"
                                class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                                onclick="location.href
                                = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/new'))?>';"
                                data-ui-id="add-button">
                                    <span><?= $escaper->escapeHtml(__("Add New Method")); ?></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="shipping-methods-grid">
                    <?= /* @noEscape */ $block->getChildHtml('sellership_rates_list_front'); ?>
                </div>
            </div>

            <!-- Free Shipping Threshold Tab -->
            <div id="free-shipping-threshold" class="wk-mp-tab-pane">
                <div class="page-main-actions">
                    <div class="page-actions-placeholder"></div>
                    <div class="page-actions" data-ui-id="page-actions-toolbar-content-header">
                        <div class="page-actions-inner" data-title='<?= $escaper->escapeHtml(__("New Free Shipping Threshold")); ?>'>
                            <div class="page-actions-buttons">
                                <button id="add-threshold" title='<?= $escaper->escapeHtml(__("Add New Threshold")); ?>' type="button"
                                class="action- scalable primary wk-ui-grid-btn wk-ui-grid-btn-primary"
                                onclick="location.href
                                = '<?= $escaper->escapeHtml($block->getUrl('coditron_customshippingrate/shiptablerates/newthreshold'))?>';"
                                data-ui-id="add-threshold-button">
                                    <span><?= $escaper->escapeHtml(__("Add New Threshold")); ?></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="threshold-grid">
                    <?= /* @noEscape */ $block->getChildHtml('sellership_threshold_list_front'); ?>
                </div>
            </div>
        </div>

    <?php else: ?>
        <h2 class="wk-mp-error-msg">
            <?= $escaper->escapeHtml(__("To Become Seller Please Contact to Admin.")); ?>
        </h2>
    <?php endif; ?>
</div>

<style>
.wk-mp-tabs-container {
    margin-bottom: 20px;
}

.wk-mp-tabs-nav {
    display: flex;
    border-bottom: 2px solid #e3e3e3;
    background: #f8f8f8;
}

.wk-mp-tab-btn {
    background: none;
    border: none;
    padding: 15px 25px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #666;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.wk-mp-tab-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.wk-mp-tab-btn.active {
    color: #1979c3;
    border-bottom-color: #1979c3;
    background: #fff;
}

.wk-mp-tab-content {
    background: #fff;
    padding: 0;
}

.wk-mp-tab-pane {
    display: none;
}

.wk-mp-tab-pane.active {
    display: block;
}
</style>

<script>
require(['jquery'], function($) {
    $(document).ready(function() {
        // Tab switching functionality
        $('.wk-mp-tab-btn').on('click', function() {
            var targetTab = $(this).data('tab');
            
            // Update active tab button
            $('.wk-mp-tab-btn').removeClass('active');
            $(this).addClass('active');
            
            // Update active tab content
            $('.wk-mp-tab-pane').removeClass('active');
            $('#' + targetTab).addClass('active');
            
            // Load appropriate grid content
            if (targetTab === 'shipping-methods') {
                loadShippingMethodsGrid();
            } else if (targetTab === 'free-shipping-threshold') {
                loadThresholdGrid();
            }
        });
        
        // Load initial content
        loadShippingMethodsGrid();
    });
    
    function loadShippingMethodsGrid() {
        // This will be handled by the UI component
        if (!$('#shipping-methods-grid').hasClass('loaded')) {
            $('#shipping-methods-grid').addClass('loaded');
        }
    }
    
    function loadThresholdGrid() {
        // This will be handled by the UI component
        if (!$('#threshold-grid').hasClass('loaded')) {
            $('#threshold-grid').addClass('loaded');
        }
    }
});
</script>
