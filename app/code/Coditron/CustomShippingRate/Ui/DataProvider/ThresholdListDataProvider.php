<?php
/**
 * Data provider for Free Shipping Threshold listing
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Ui\DataProvider;

use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Magento\Framework\Api\Filter;
use Magento\Ui\DataProvider\AbstractDataProvider;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class ThresholdListDataProvider extends AbstractDataProvider
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @param string $name
     * @param string $primaryFieldName
     * @param string $requestFieldName
     * @param CollectionFactory $collectionFactory
     * @param MarketplaceHelper $marketplaceHelper
     * @param array $meta
     * @param array $data
     */
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        MarketplaceHelper $marketplaceHelper,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        $this->marketplaceHelper = $marketplaceHelper;
        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    /**
     * Get data
     *
     * @return array
     */
    public function getData()
    {
        if (isset($this->loadedData)) {
            return $this->loadedData;
        }

        $sellerId = $this->marketplaceHelper->getCustomerId();
        
        // Filter collection to show only records with min_order_amount > 0 for current seller
        $this->collection->addFieldToFilter('seller_id', $sellerId)
                        ->addFieldToFilter('min_order_amount', ['gt' => 0]);

        $items = $this->collection->toArray();
        
        // Process the data to show only relevant fields for threshold view
        foreach ($items['items'] as &$item) {
            // Format countries for display
            if (isset($item['countries'])) {
                $countries = explode(',', $item['countries']);
                $item['countries'] = implode(', ', $countries);
            }
            
            // Format min_order_amount
            if (isset($item['min_order_amount'])) {
                $item['min_order_amount'] = number_format((float)$item['min_order_amount'], 2);
            }
        }

        $this->loadedData = $items;
        return $this->loadedData;
    }

    /**
     * Add filter
     *
     * @param Filter $filter
     * @return void
     */
    public function addFilter(Filter $filter)
    {
        if ($filter->getField() !== 'fulltext') {
            $this->collection->addFieldToFilter(
                $filter->getField(),
                [$filter->getConditionType() => $filter->getValue()]
            );
        } else {
            $value = trim($filter->getValue());
            $this->collection->addFieldToFilter(
                [
                    ['attribute' => 'countries'],
                    ['attribute' => 'min_order_amount']
                ],
                [
                    ['like' => "%{$value}%"],
                    ['like' => "%{$value}%"]
                ]
            );
        }
    }
}
