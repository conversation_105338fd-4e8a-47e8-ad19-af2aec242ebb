<?php
/**
 * Controller for editing free shipping threshold
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class EditThreshold extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Free Shipping Threshold Edit action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        if (!$this->_marketplaceHelper->isSeller()) {
            return $this->resultRedirectFactory->create()->setPath(
                'marketplace/account/becomeseller',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }

        $resultPage = $this->_resultPageFactory->create();
        if ($this->_marketplaceHelper->getIsSeparatePanel()) {
            $resultPage->addHandle('mpsellership_layout2_threshold_edit');
        }

        $resultPage->getConfig()->getTitle()->set(__('Edit Free Shipping Threshold'));
        return $resultPage;
    }
}
