<?php
/**
 * Simple test script to verify free shipping thresholds functionality
 */

require_once 'app/bootstrap.php';

use Magento\Framework\App\Bootstrap;
use Magento\Framework\App\ObjectManager;

$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();

try {
    // Test if we can get the collection factory
    $collectionFactory = $objectManager->get(\Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory::class);
    echo "✓ Collection factory instantiated successfully\n";
    
    // Test if we can create a collection
    $collection = $collectionFactory->create();
    echo "✓ Collection created successfully\n";
    
    // Test filtering for free shipping thresholds
    $collection->addFieldToFilter('free_shipping', 1)
               ->addFieldToFilter('min_order_amount', ['gt' => 0]);
    
    echo "✓ Filters applied successfully\n";
    echo "Found " . $collection->getSize() . " free shipping thresholds\n";
    
    // Test if we can instantiate the block (this will test the constructor)
    $block = $objectManager->get(\Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method\Form::class);
    echo "✓ Block instantiated successfully\n";
    
    echo "\n✅ All tests passed! The implementation should work correctly.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
