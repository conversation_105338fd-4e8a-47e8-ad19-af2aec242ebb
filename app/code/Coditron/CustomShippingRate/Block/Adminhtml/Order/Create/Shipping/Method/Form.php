<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method;

use Magento\Quote\Model\Quote\Address\Rate;
use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;

/**
 * Class Form
 * @package Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method
 */
class Form extends \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\Form
{
    /** @var Rate|false **/
    protected $activeMethodRate;

    /** @var ShipTableRatesCollectionFactory */
    protected $shipTableRatesCollectionFactory;

    /**
     * Constructor
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Backend\Model\Session\Quote $sessionQuote
     * @param \Magento\Sales\Model\AdminOrder\Create $orderCreate
     * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
     * @param \Magento\Tax\Helper\Data $taxData
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Model\Session\Quote $sessionQuote,
        \Magento\Sales\Model\AdminOrder\Create $orderCreate,
        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
        \Magento\Tax\Helper\Data $taxData,
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory,
        array $data = []
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        parent::__construct($context, $sessionQuote, $orderCreate, $priceCurrency, $taxData, $data);
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function getActiveCustomShippingRateMethod()
    {
        $rate = $this->getActiveMethodRate();
        return $rate && $rate->getCarrier() == Carrier::CODE ? $rate->getMethod() : '';
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function getActiveCustomShippingRatePrice()
    {
        $rate = $this->getActiveMethodRate();
        return $this->getActiveCustomShippingRateMethod() && $rate->getPrice() ? $rate->getPrice() * 1 : '';
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function isCustomShippingRateActive()
    {
        if (empty($this->activeMethodRate)) {
            $this->activeMethodRate = $this->getActiveMethodRate();
        }

        return $this->activeMethodRate && $this->activeMethodRate->getCarrier() == Carrier::CODE ? true : false;
    }

    /**
     * Retrieve array of shipping rates groups
     *
     * @return array
     */
    public function getGroupShippingRates()
    {
        $rates = $this->getShippingRates();

        if (array_key_exists(Carrier::CODE, $rates)) {
            if (!$this->isCustomShippingRateActive()) {
                unset($rates[Carrier::CODE]);
            } else {
                $activeRateMethod = $this->getActiveCustomShippingRateMethod();
                foreach ($rates[Carrier::CODE] as $key => $rate) {
                    if ($rate->getMethod() != $activeRateMethod) {
                        unset($rates[Carrier::CODE][$key]);
                    }
                }
            }
        }

        return $rates;
    }

    /**
     * Get available free shipping thresholds
     *
     * @return array
     */
    public function getFreeShippingThresholds()
    {
        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0]);

        $thresholds = [];
        foreach ($collection as $threshold) {
            $thresholds[] = [
                'id' => $threshold->getShiptableratesId(),
                'courier' => $threshold->getCourier(),
                'service_type' => $threshold->getServiceType(),
                'countries' => $threshold->getCountries(),
                'min_amount' => $threshold->getMinOrderAmount(),
                'seller_id' => $threshold->getSellerId()
            ];
        }

        return $thresholds;
    }
}
