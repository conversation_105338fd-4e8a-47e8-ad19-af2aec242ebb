<?php
/**
 * Block for tabbed shipping rates interface
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

class TabbedRates extends Template
{
    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @param Context $context
     * @param MarketplaceHelper $marketplaceHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        MarketplaceHelper $marketplaceHelper,
        array $data = []
    ) {
        $this->marketplaceHelper = $marketplaceHelper;
        parent::__construct($context, $data);
    }

    /**
     * Get marketplace helper
     *
     * @return MarketplaceHelper
     */
    public function getMpHelper()
    {
        return $this->marketplaceHelper;
    }

    /**
     * Check if current user is a seller
     *
     * @return bool
     */
    public function isSeller()
    {
        return $this->marketplaceHelper->isSeller();
    }

    /**
     * Get URL for new shipping method
     *
     * @return string
     */
    public function getNewShippingMethodUrl()
    {
        return $this->getUrl('coditron_customshippingrate/shiptablerates/new');
    }

    /**
     * Get URL for new threshold
     *
     * @return string
     */
    public function getNewThresholdUrl()
    {
        return $this->getUrl('coditron_customshippingrate/shiptablerates/newthreshold');
    }
}
