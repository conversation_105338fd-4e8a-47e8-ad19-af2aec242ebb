<?php

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Action\Context;
use Magento\Ui\Component\MassAction\Filter;
use Coditron\CustomShippingRate\Helper\Data as HelperData;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;

class MassDelete extends \Magento\Framework\App\Action\Action
{
    /**
     * @var Filter
     */
    protected $_filter;

    /**
     * @var HelperData
     */
    protected $_helper;

    /**
     * @var CollectionFactory
     */
    protected $_collectionFactory;

    /**
     * @param Context $context
     * @param Filter $filter
     * @param HelperData $helper
     * @param CollectionFactory $collectionFactory
     */
    public function __construct(
        Context $context,
        Filter $filter,
        HelperData $helper,
        CollectionFactory $collectionFactory
    ) {
        $this->_filter = $filter;
        $this->_helper = $helper;
        $this->_collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * Execute action.
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        try {
            $shiprateIds = $this->getRequest()->getParam('shiprate_mass_action');
            $sellerShiprateCollection = $this->_collectionFactory->create();
            if ($shiprateIds) {
                $collection = $sellerShiprateCollection->addFieldToFilter('shiptablerates_id', ['in' => $shiprateIds]);
            } else {
                $collection = $this->_filter->getCollection($sellerShiprateCollection);
            }
            $countRecord = $collection->getSize();
            $collection->removeShiprates();
            $this->_helper->clearCache();
            $this->messageManager->addSuccess(
                __(
                    'A total of %1 record(s) have been deleted.',
                    $countRecord
                )
            );

            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        } catch (\Exception $e) {
            $this->messageManager->addError($e->getMessage());

            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
