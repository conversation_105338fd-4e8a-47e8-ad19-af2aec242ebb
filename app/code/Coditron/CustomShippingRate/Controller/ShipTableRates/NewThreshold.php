<?php
/**
 * Controller for creating new free shipping threshold
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class NewThreshold extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Create New Free Shipping Threshold action
     */
    public function execute()
    {
        $resultForward = $this->resultForwardFactory->create();
        $resultForward->forward('editthreshold');
        return $resultForward;
    }
}
