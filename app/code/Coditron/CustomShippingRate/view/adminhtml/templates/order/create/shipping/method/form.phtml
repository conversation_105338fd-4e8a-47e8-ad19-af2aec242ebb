<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

/**
 * @var $block \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\Form
 * @var \Magento\Framework\View\Helper\SecureHtmlRenderer $secureRenderer
 */
?>
<?php /** @var $block \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\Form */ ?>
<?php
/** @var \Magento\Tax\Helper\Data $taxHelper */
$taxHelper = $block->getData('taxHelper');
?>
<?php $_shippingRateGroups = $block->getGroupShippingRates(); ?>
<?php if ($_shippingRateGroups): ?>
    <div id="order-shipping-method-choose" class="control">
        <dl class="admin__order-shipment-methods">
        <?php foreach ($_shippingRateGroups as $code => $_rates): ?>
            <dt class="admin__order-shipment-methods-title"><?= $block->escapeHtml($block->getCarrierName($code)) ?></dt>
            <dd class="admin__order-shipment-methods-options">
                <ul class="admin__order-shipment-methods-options-list">
                <?php foreach ($_rates as $_rate): ?>
                    <?php $_radioProperty = 'name="order[shipping_method]" type="radio"' ?>
                    <?php $_code = $_rate->getCode() ?>
                    <li class="admin__field-option">
                       <?php if ($_rate->getErrorMessage()): ?>
                           <div class="messages">
                               <div class="message message-error error">
                                   <div><?= $block->escapeHtml($_rate->getErrorMessage()) ?></div>
                               </div>
                           </div>
                       <?php else: ?>
                            <?php $_checked = $block->isMethodActive($_code) ? 'checked="checked"' : '' ?>
                            <input <?= /* @noEscape */ $_radioProperty ?>
                                value="<?= $block->escapeHtmlAttr($_code) ?>"
                                id="s_method_<?= $block->escapeHtmlAttr($_code) ?>" <?= /* @noEscape */ $_checked ?>
                                                                 class="admin__control-radio required-entry"/>
                            <label class="admin__field-label" for="s_method_<?= $block->escapeHtmlAttr($_code) ?>">
                                <?= $block->escapeHtml($_rate->getMethodTitle() ?
                                    $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
                                <strong>
                                    <?php $_excl = $block->getShippingPrice(
                                        $_rate->getPrice(),
                                        $taxHelper->displayShippingPriceIncludingTax()
                                    ); ?>
                                    <?php $_incl = $block->getShippingPrice($_rate->getPrice(), true); ?>

                                    <?= /* @noEscape */ $_excl ?>
                                    <?php if ($taxHelper->displayShippingBothPrices() && $_incl != $_excl): ?>
                                        (<?= $block->escapeHtml(__('Incl. Tax')) ?> <?= /* @noEscape */ $_incl ?>)
                                    <?php endif; ?>
                                </strong>
                            </label>
                            <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
                                'onclick',
                                "order.setShippingMethod(this.value)",
                                'input#s_method_' . $block->escapeHtmlAttr($_code)
                            ) ?>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
                </ul>
            </dd>
        <?php endforeach; ?>
            <?php /*------- Start Coditron --------*/ ?>
            <?php if ($this->helper('Coditron\CustomShippingRate\Helper\Data')->isEnabled($this->getQuote()->getStore()->getStoreId())): ?>
                <dt class="admin__order-shipment-methods-title">
                    <hr/>
                </dt>
                <dd class="admin__order-shipment-methods-options" style="margin-top:10px;">
                    <ul class="admin__order-shipment-methods-options-list">
                        <li>

                            <p><strong><?php echo __('CUSTOM SHIPPING RATE'); ?></strong></p>
                            <p>
                                <strong><?php echo __('Carrier/Rate:'); ?></strong>

                                <select id="custom-shipment-title" class="admin__control-select required-entry">
                                    <!--option>-- please choose --</option-->
                                    <?php foreach ($this->helper('Coditron\CustomShippingRate\Helper\Data')->getShippingType($this->getQuote()->getStore()->getStoreId()) as $shippingType) :?>
                                        <option value="<?php echo $shippingType['code'] ?>" <?php echo $block->getActiveCustomShippingRateMethod() == $shippingType['code'] ? ' selected' : '' ?>>
                                            <?php echo __($shippingType['title']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <input id="custom-shipment-rate" type="text" style="width:75px; padding:3px;" placeholder="9.99" value="<?php echo $block->getActiveCustomShippingRatePrice() ?>" class="input-text item-price required-entry" />
                                <button id="custom-shipping-button" type="button" class="scalable "
                                        onclick="order.setShippingMethod(getCustomShippingCost())" ><span><?= __('Set') ?></span></button>
                            </p>

                        </li>

                    <ul class="messages" style="display:none;">
                        <li class="notice-msg"><?php echo __('Note: This will override any shipping method selected above.'); ?></li>
                    </ul>
                </dd>
            <?php endif; ?>

            <?php /*------- Start Free Shipping Thresholds --------*/ ?>
            <?php $freeShippingThresholds = $block->getFreeShippingThresholds(); ?>
            <?php if (!empty($freeShippingThresholds)): ?>
                <dt class="admin__order-shipment-methods-title">
                    <hr/>
                </dt>
                <dd class="admin__order-shipment-methods-options" style="margin-top:10px;">
                    <ul class="admin__order-shipment-methods-options-list">
                        <li>
                            <p><strong><?php echo __('FREE SHIPPING THRESHOLDS'); ?></strong></p>
                            <p><?php echo __('Select a free shipping threshold:'); ?></p>

                            <?php foreach ($freeShippingThresholds as $threshold): ?>
                                <?php
                                $thresholdCode = 'freeshipping_threshold_' . $threshold['id'];
                                $countries = is_array($threshold['countries']) ? implode(', ', $threshold['countries']) : $threshold['countries'];
                                ?>
                                <div style="margin: 5px 0;">
                                    <input type="radio"
                                           name="order[shipping_method]"
                                           value="<?= $block->escapeHtmlAttr($thresholdCode) ?>"
                                           id="threshold_<?= $block->escapeHtmlAttr($threshold['id']) ?>"
                                           onclick="order.setShippingMethod(this.value)" />
                                    <label for="threshold_<?= $block->escapeHtmlAttr($threshold['id']) ?>">
                                        <strong><?= $block->escapeHtml($threshold['courier']) ?> - <?= $block->escapeHtml($threshold['service_type']) ?></strong><br/>
                                        <?= $block->escapeHtml(__('Countries: %1', $countries)) ?><br/>
                                        <?= $block->escapeHtml(__('Free shipping for orders over: $%1', $threshold['min_amount'])) ?>
                                    </label>
                                </div>
                            <?php endforeach; ?>
                        </li>
                    </ul>
                </dd>
            <?php endif; ?>
            <?php /*------- End Free Shipping Thresholds --------*/ ?>

            <?php /*------- End Coditron --------*/ ?>

        </dl>
    </div>
    <?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display:none", 'div#order-shipping-method-choose') ?>
    <?php if ($_rate = $block->getActiveMethodRate()): ?>
        <div id="order-shipping-method-info" class="order-shipping-method-info">
            <dl class="admin__order-shipment-methods">
                <dt class="admin__order-shipment-methods-title">
                    <?= $block->escapeHtml($block->getCarrierName($_rate->getCarrier())) ?>
                </dt>
                <dd class="admin__order-shipment-methods-options">
                    <?= $block->escapeHtml($_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
                    <strong>
                        <?php $_excl = $block->getShippingPrice(
                            $_rate->getPrice(),
                            $taxHelper->displayShippingPriceIncludingTax()
                        ); ?>
                        <?php $_incl = $block->getShippingPrice($_rate->getPrice(), true); ?>

                        <?= /* @noEscape */ $_excl ?>
                        <?php if ($taxHelper->displayShippingBothPrices() && $_incl != $_excl): ?>
                            (<?= $block->escapeHtml(__('Incl. Tax')) ?> <?= /* @noEscape */ $_incl ?>)
                        <?php endif; ?>
                    </strong>
                </dd>
            </dl>
            <a href="#"
               class="action-default">
                <span><?= $block->escapeHtml(__('Click to change shipping method')) ?></span>
            </a>
        </div>
        <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
            'onclick',
            "$('order-shipping-method-info').hide();$('order-shipping-method-choose').show();event.preventDefault()",
            'div#order-shipping-method-info a.action-default'
        ) ?>
    <?php else: ?>
        <?php $scriptString = <<<script
require(['prototype'], function(){
    $('order-shipping-method-choose').show();
});
script;
        ?>
        <?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
    <?php endif; ?>
<?php elseif ($block->getIsRateRequest()): ?>
    <div class="order-shipping-method-summary">
        <strong class="order-shipping-method-not-available">
            <?= $block->escapeHtml(__('Sorry, no quotes are available for this order.')) ?>
        </strong>
    </div>
<?php else: ?>
    <div id="order-shipping-method-summary" class="order-shipping-method-summary">
        <a href="#" class="action-default">
            <span><?= $block->escapeHtml(__('Get shipping methods and rates')) ?></span>
        </a>
        <input type="hidden" name="order[has_shipping]" value="" class="required-entry" />
    </div>
    <?= /* @noEscape */ $secureRenderer->renderEventListenerAsTag(
        'onclick',
        "order.loadShippingRates();event.preventDefault();",
        'div#order-shipping-method-summary a.action-default'
    ) ?>
<?php endif; ?>
<div id="shipping-method-overlay" class="order-methods-overlay">
    <span><?= $block->escapeHtml(__('You don\'t need to select a shipping method.')) ?></span>
</div>
<?= /* @noEscape */ $secureRenderer->renderStyleAsTag("display: none;", 'div#shipping-method-overlay') ?>
<?php $scriptString = <<<script
    require(["Magento_Sales/order/create/form"], function(){

script;
$scriptString .= "order.overlay('shipping-method-overlay', " . ($block->getQuote()->isVirtual() ? 'false' : 'true') .
    ');' . PHP_EOL;
$scriptString .= "order.overlay('address-shipping-overlay', " . ($block->getQuote()->isVirtual() ? 'false' : 'true') .
 ');' . PHP_EOL;
$scriptString .= "order.isOnlyVirtualProduct = " . ($block->getQuote()->isVirtual() ? 'true' : 'false') . ';' . PHP_EOL;
$scriptString .= <<<script
    });
script;
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>

<?php /*------- Start Coditron --------*/ ?>
<?php $scriptString = <<<script
    function getCustomShippingCost(){
        var rate = $('custom-shipment-rate').value;
        var type  = $('custom-shipment-title')[$('custom-shipment-title').selectedIndex].value;
        var code = 'customshippingrate_' + type;
        var desc = '';

        return '{"rate" : "' + rate + '", "code" : "' + code  + '", "type" : "' + type + '", "description" : "' + desc + '"}';
    }
script;
?>
<?= /* @noEscape */ $secureRenderer->renderTag('script', [], $scriptString, false) ?>
<?php /*------- End Coditron --------*/ ?>
